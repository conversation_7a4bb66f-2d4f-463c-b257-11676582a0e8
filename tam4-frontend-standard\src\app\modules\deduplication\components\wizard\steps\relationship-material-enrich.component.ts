import {Component, computed, inject, Input, OnInit, signal, Signal} from '@angular/core';
import {MaterialsEditorModule} from '../../../../materials-editor/materials-editor.module';
import {SmartCreationMaterialDetail, SmartFieldConfiguration} from '../../../../smart-creation/models/smart-creation.types';
import {ViewModeEnum} from '../../../../materials-editor/models/material-editor.types';
import {TamApePageType} from '../../../../../models';
import {Tam4SelectorConfig, TamAbstractReduxComponent} from '../../../../../components';
import {DeduplicationSelectors} from '../../../store/deduplication.selectors';
import {SelectorMap} from '@creactives/models';
import {TranslateModule, TranslateService} from '@ngx-translate/core';
import {Tam4TranslationService} from '@creactives/tam4-translation-core';
import {select, Store} from '@ngrx/store';
import {DeduplicationState} from '../../../store/deduplication.state';
import {DeduplicationService} from '../../../services/deduplication.service';
import {InternalCommonModule} from '../../../../common/iternal-common.module';
import {
    DeduplicationFieldConfiguration,
    DynamicInputConfig,
    EnrichmentTableColumn,
    EnrichmentTableRow
} from '../../../models/deduplication.models';
import {DynamicFormInputFactory} from '../../../../materials-editor/service/dynamic-form-input.factory';
import {FormGroup, Validators} from '@angular/forms';
import {ProgressSpinnerModule} from 'primeng/progressspinner';
import {MessageModule} from 'primeng/message';
import {DocumentData, DynamicComponentWrapper} from '../../../../materials-editor/dynamic-components';
import {ObjectsUtils} from '../../../../../utils';
import {Observable, of} from 'rxjs';
import {
    AlternativeUnitsOfMeasure,
    SmartCreationDropdownDataRequest
} from '../../../../smart-creation/models/smart-creation-validation.types';
import {SmartCreationFormControl} from '../../../../smart-creation/models/smart-creation-form.types';
import {needFlatArray} from '../../../../smart-creation/commons/smart-creation.constants';
import {normalizeControlsFormGroup, setUseTranslatePipe} from '../../../../materials-editor/common/materials-editor.function';
import {SmartCreationDao} from '../../../../smart-creation/smart-creation.dao';
import {getCurrentLanguage} from '../../../../layout/store/reducers';
import {getFallbackLanguages} from '../../../../layout/store/profile/profile.state';
import {catchError, switchMap, take} from 'rxjs/operators';
import {RadioButtonModule} from 'primeng/radiobutton';

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'materials', selector: DeduplicationSelectors.getMaterials},
  {key: 'step', selector: DeduplicationSelectors.getStep},
  {key: 'subSteps', selector: DeduplicationSelectors.getSubSteps},
  {key: 'currentClient', selector: DeduplicationSelectors.getCurrentClient},
  {key: 'enrichmentMaterialDetails', selector: DeduplicationSelectors.getEnrichmentMaterialDetails},
  {key: 'enrichedMaterialsData', selector: DeduplicationSelectors.getEnrichedMaterialData},
  {key: 'showEnrichedData', selector: DeduplicationSelectors.getShowEnrichedData},
  {key: 'loading', selector: DeduplicationSelectors.getIsLoading}
];

@Component({
  selector: 'relationship-material-enrich',
  styleUrls: ['./relationship-material-enrich.component.scss'],
  template: `
      <div class="material-enrichment-container">
        <div class="enrichment-header">
          <h3>{{ 'deduplication.enrichment.title' | translate }}</h3>
          @if (substepsLength() > 1) {
            <div class="client-pagination-indicator">
              <span class="pagination-label">{{ 'deduplication.enrichment.clientPagination' | translate }}:</span>
              <span class="pagination-display">{{ clientPaginationDisplay() }}</span>
            </div>
          }
        </div>

        @if (hasData() && tableRows()?.length > 0 && tableColumns()?.length > 0) {
          <p-table [value]="tableRows()"
                   styleClass="p-datatable-compat enrichment-table"
                   [scrollable]="true"
                   scrollHeight="600px"
                   [rowGroupMode]="'subheader'"
                   groupRowsBy="groupTabLabel"
          >
            <ng-template pTemplate="header">
              <tr>
                <th pFrozenColumn>{{'deduplication.enrichment.client' | translate}}: {{signals?.currentClient() ?? ('deduplication.enrichment.noClient' | translate)}}</th>
                <th pFrozenColumn>
                  {{'deduplication.enrichment.showEnriched' | translate}}
                  <p-inputSwitch
                      [ngModel]="showEnrichedData()"
                      (ngModelChange)="onToggleShowEnrichedData($event)" />
                </th>
                @for (column of tableColumns(); track column.type) {
                  @if (column.type === 'SECONDARY') {
                    <th>
                      <p-checkbox
                          [binary]="true"
                          [ngModel]="columnSelectionStates()[column.materialKey]?.allSelected || false"
                          (onChange)="onColumnSelectAll(column, $event.checked)"
                          [disabled]="!column.materialKey || showEnrichedData()">
                      </p-checkbox>
                      <span class="m-1 mt-3">{{column.materialKey}}</span>
                    </th>
                  }
                }
              </tr>
            </ng-template>

            <ng-template pTemplate="groupheader" let-rowData>
              <tr pRowGroupHeader class="p-rowgroup-header">
                <td [attr.colspan]="tableColumns().length">
                  <span class="font-bold ml-2">{{ rowData.groupTabLabel }}</span>
                </td>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-row>
              <tr>
                @for (column of tableColumns(); track column.type) {
                    @switch (column.type) {
                      @case ('LABEL') {
                        <td pFrozenColumn>
                            <span >{{ row.label | attributeNameTranslate}}</span>
                        </td>
                      }
                      @case ('PRIMARY') {
                        <td class="primary">
                          @if (getPrimaryInputConfig(row.primary); as inputConfig) {
                            @if (inputConfig.component && inputConfig.params && inputConfig?.formControl) {
                              <span scDynamicContainerTemplate
                                    [dynamicComponent]="inputConfig.component"
                                    [dynamicParams]="inputConfig.params"
                                    [editable]="inputConfig.editable && editable && !showEnrichedData()"
                                    [viewMode]="type"
                                    [showEmptyValues]="showEmptyValues"
                                    [mandatory]="inputConfig.params?.mandatory"
                                    [coreAttribute]="inputConfig.params?.componentParams?.coreAttribute"
                                    [disableCopy]="true"
                              >
                              </span>
                            }
                          } @else {
                            <span class="raw-value">{{ row.primary.value || '' }}</span>
                          }
                        </td>
                      }
                      @default {
                        <td class="secondary">
                          @if (column.type === 'SECONDARY') {
                            @if (getSecondaryForColumn(row, column); as secondaryField) {
                              @if (getSecondaryInputConfig(secondaryField, column.materialKey); as inputConfig) {
                                @if (inputConfig.component && inputConfig.params && inputConfig?.formControl) {
                                    <div class="secondary-field">
                                        @if (secondaryField.editable && secondaryField.value) {
                                            <p-checkbox
                                                    [ngModel]="isSecondaryFieldSelected(row.fieldId, secondaryField.id, column)"
                                                    (ngModelChange)="onSecondaryFieldSelectionChange(row, column, secondaryField, $event)"
                                                    [name]="secondaryField.id"
                                                    [disabled]="!secondaryField.editable || showEnrichedData()"
                                                    [binary]="true">
                                            </p-checkbox>
                                        }
                                      <span scDynamicContainerTemplate
                                            [dynamicComponent]="inputConfig.component"
                                            [dynamicParams]="inputConfig.params"
                                            [editable]="false"
                                            [viewMode]="type"
                                            [showEmptyValues]="showEmptyValues"
                                            [mandatory]="inputConfig.params?.mandatory"
                                            [coreAttribute]="inputConfig.params?.componentParams?.coreAttribute"
                                            [disableCopy]="true"
                                            [class]="secondaryField.editable && secondaryField.value ? 'ml-1 mt-2' : ''"
                                      >
                                      </span>
                                  </div>
                                }
                              } @else {
                                <span [class]="secondaryField.editable && secondaryField.value ? 'ml-1 mt-2' : ''">{{ secondaryField.value | attributeValueTranslate : secondaryField.id }}</span>
                              }
                            } @else {
                              <span class="empty-cell"></span>
                            }
                          }
                        </td>
                      }
                    }
                }
              </tr>
            </ng-template>
            <ng-template pTemplate="footer">
              <tr>
                <td colspan="2" pFrozenColumn>

                </td>
                <td colspan="{{ tableColumns().length - 2 }}"></td>
              </tr>
            </ng-template>
          </p-table>
            <div class="flex align-items-center justify-content-between w-full">
                <div class="flex align-items-center gap-2">
                    {{ 'deduplication.enrichment.hideNonEditables' | translate }}
                    <p-checkbox
                            [binary]="true"
                            inputId="nonEditableCheck"
                            [ngModel]="hideNonEditable()"
                            (ngModelChange)="hideNonEditable.set($event)">
                    </p-checkbox>
                </div>
                <p-button
                        label="{{ 'deduplication.steps.button.moveValues' | translate }}"
                        icon="pi pi-arrow-left"
                        [disabled]="!hasSelectedValues() || showEnrichedData()"
                        (onClick)="onMoveAllValues()">
                </p-button>
            </div>
        } @else {
          <p-message severity="info"
                     [text]="'deduplication.enrichment.noData' | translate">
          </p-message>
        }
      </div>
    `,
  imports: [
    MaterialsEditorModule,
    InternalCommonModule,
    ProgressSpinnerModule,
    MessageModule,
    RadioButtonModule,
    TranslateModule
  ],
  standalone: true
})
export class RelationshipMaterialEnrich extends TamAbstractReduxComponent<SelectorMap> implements OnInit {

  @Input() mdDomain!: string;

  enrichmentMaterialDetails = computed(() => this.signals?.enrichmentMaterialDetails());

  tableRows = computed(() => {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }
    return this.transformDataToTableRows();
  });

  tableColumns = computed(() => {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }
    return this.generateTableColumns();
  });

  isLoading = computed(() => !this.enrichmentMaterialDetails());

  hasData = computed(() => {
    const data = this.enrichmentMaterialDetails();
    return data && Array.isArray(data) && data.length > 0 &&
        data.some(group => group.rows && group.rows.length > 0);
  });

  clientPaginationDisplay = computed(() => {
    const currentIndex = this.signals.step().subStepPage ?? 0;
    const total = this.substepsLength() ?? 0;
    return currentIndex >= 0 ? `${currentIndex + 1}/${total}` : '1/1';
  });

  hideNonEditable = signal<boolean>(true);
  showEnrichedData = computed(() => this.signals?.showEnrichedData() || false);

  substepsLength = computed(() => this.signals.subSteps().length || 0);

  columnSelectionStates = computed(() => {
    const rows = this.tableRows();
    const columns = this.tableColumns();
    const states: { [materialKey: string]: { allSelected: boolean, someSelected: boolean } } = {};

    columns.forEach(column => {
      if (column.type === 'SECONDARY' && column.materialKey) {
        const editableRows = rows.filter(row => {
          const secondaryField = this.getSecondaryForColumn(row, column);
          return secondaryField && secondaryField.editable;
        });

        if (editableRows.length === 0) {
          states[column.materialKey] = { allSelected: false, someSelected: false };
        } else {
          const selectedCount = editableRows.filter(row => {
            const secondaryField = this.getSecondaryForColumn(row, column);
            return secondaryField && secondaryField.selected;
          }).length;

          states[column.materialKey] = {
            allSelected: selectedCount === editableRows.length,
            someSelected: selectedCount > 0 && selectedCount < editableRows.length
          };
        }
      }
    });

    return states;
  });

  hasSelectedValues = computed(() => {
    return this.hasSelectedSecondaryValues();
  });

  primaryInputConfigs: Signal<Map<string, DynamicInputConfig | null>> = computed(() => {
    const enrichmentData = this.enrichmentMaterialDetails();
    const configMap = new Map<string, DynamicInputConfig | null>();

    if (enrichmentData) {
      enrichmentData.forEach(group => {
        group.rows?.forEach(rowData => {
          const primaryKeys = Object.keys(rowData.primary || {});
          const primaryField = primaryKeys.length > 0 ? rowData.primary[primaryKeys[0]] : null;
          if (primaryField?.id) {
            const config = this.createDynamicInputConfig(primaryField, primaryField.editable);
            configMap.set(primaryField.id, config);
          }
        });
      });
    }

    return configMap;
  });

  secondaryInputConfigs: Signal<Map<string, DynamicInputConfig | null>> = computed(() => {
    const enrichmentData = this.enrichmentMaterialDetails();
    const configMap = new Map<string, DynamicInputConfig | null>();

    if (enrichmentData) {
      enrichmentData.forEach(group => {
        group.rows?.forEach(rowData => {
          if (rowData.secondaries) {
            Object.keys(rowData.secondaries).forEach(secondaryMaterialId => {
              const secondaryField = rowData.secondaries[secondaryMaterialId];
              if (secondaryField?.id) {
                const config = this.createDynamicInputConfig(secondaryField, false);
                const compositeKey = `${secondaryField.id}_${secondaryMaterialId}`;
                configMap.set(compositeKey, config);
              }
            });
          }
        });
      });
    }

    return configMap;
  });

  // TODO: optimize or move to backend
  disabledAttributes = [
    '4_TAM_UnitOfMeasure',
    '4_TAM_AlternativeUnitOfMeasure',
    '4_SDM_Client'
  ];

  type: ViewModeEnum = ViewModeEnum.EDIT;
  page: string = TamApePageType.EDIT;

  service = inject(DeduplicationService);
  dynamicFormInputFactory = inject(DynamicFormInputFactory);
  smartCreationDao = inject(SmartCreationDao);
  materialDetails: SmartCreationMaterialDetail;

  showEmptyValues = true;
  editable = true;

  constructor(protected translate: TranslateService,
              protected tamTranslate: Tam4TranslationService,
              protected store: Store<DeduplicationState>) {
    super(translate, tamTranslate, store, storeSelectors);
  }

  ngOnInit() {}

  ngOnDestroy() {
    super.ngOnDestroy();
  }

  isSecondaryFieldSelected(fieldId: string, secondaryFieldId: string, column: EnrichmentTableColumn): boolean {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return false;
    }

    for (const group of data) {
      if (group.rows && Array.isArray(group.rows)) {
        for (const row of group.rows) {
          if (row.id === fieldId && row.secondaries) {
            const secondary = row.secondaries[column.materialKey];
            if (secondary?.id === secondaryFieldId) {
              return !!secondary.selected;
            }
          }
        }
      }
    }

    return false;
  }

  private transformDataToTableRows(): EnrichmentTableRow[] {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return [];
    }

    const rows: EnrichmentTableRow[] = [];

    data.forEach(group => {
      if (!group.rows || !Array.isArray(group.rows)) {
        return;
      }

      group.rows.forEach((rowData: any) => {
        const fieldId = rowData.id;
        const primaryKeys = Object.keys(rowData.primary || {});
        const primaryField = primaryKeys.length > 0 ? rowData.primary[primaryKeys[0]] : null;

        if (!primaryField ||
            primaryField.hidden ||
            this.disabledAttributes.includes(primaryField.id) ||
            (this.hideNonEditable() && !primaryField.editable)) {
          return;
        }

        const label = primaryField.descriptionLanguages?.[0] || primaryField.label || fieldId;

        const secondaries = rowData.secondaries || {};

        rows.push({
          fieldId,
          label,
          primary: primaryField,
          secondaries,
          groupTabLabel: group.groupTabLabel,
          groupTabKey: group.groupTabKey
        });
      });
    });

    return rows;
  }

  private generateTableColumns(): EnrichmentTableColumn[] {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return [];
    }

    const columns: EnrichmentTableColumn[] = [
      { index: -1, header: 'Field', type: 'LABEL' },
      { index: 0, header: 'Primary', type: 'PRIMARY' }
    ];

    // Add secondary columns based on unique secondary material IDs
    for (const group of data) {
      if (group.rows && group.rows.length > 0) {
        const firstRow = group.rows[0];
        if (firstRow.secondaries) {
          const secondaryMaterialIds = Object.keys(firstRow.secondaries);
          secondaryMaterialIds.forEach((materialId, index) => {
            columns.push({
              index,
              header: `${materialId}`,
              materialKey: materialId,
              type: 'SECONDARY'
            });
          });
          break; // Only need first row to get column structure
        }
      }
    }
    return columns;
  }

  private createEnhancedParams(originalParams: any, additionalParams: { [key: string]: any } = {}): any {
    return {
      ...originalParams,
      ...additionalParams
    };
  }

  createDynamicInputConfig(fieldConfig: DeduplicationFieldConfiguration, isEditable: boolean): DynamicInputConfig | null {
    try {
      const formGroupInputs = this.initFormGroup(
          [fieldConfig],
          TamApePageType.EDIT,
          ViewModeEnum.EDIT,
          0,
          null,
          this.dynamicAutocompleteFn,
          null,
          null);

      if (!formGroupInputs.items.length ||
          !formGroupInputs.items[0].component ||
          !formGroupInputs.items[0].componentParams) {
        console.warn('Dynamic input config is not valid: ', fieldConfig);
        return null;
      }

      const formControlKey = fieldConfig.id;
      const formControl = formGroupInputs.formGroup.controls?.[formControlKey] as SmartCreationFormControl;
      formControl.validateErrors = [];

      if (fieldConfig.errors) {
          fieldConfig.errors.forEach((error: any) => {
              formControl.validateErrors.push({
                  field: fieldConfig.id,
                  statusMessage: error,
                  status: 'error'
              });
          });
      }

      if (!formControl) {
        console.warn('Form control not found for field:', formControlKey);
        return null;
      }

      const enhancedParams = this.createEnhancedParams(
          formGroupInputs.items[0].componentParams,
          {
            editable: isEditable,
            onInputValueEvent: (id: string) => this.onInputValueEvent(id),
            hasError: fieldConfig.hasError,
          }
      );

      return {
        component: formGroupInputs.items[0].component,
        params: enhancedParams,
        formControl,
        editable: isEditable
      };
    } catch (error) {
      console.error('Error creating dynamic input config for field:', fieldConfig.id, error);
      return null;
    }
  }

  private dynamicAutocompleteFnInternal = (source: string,
                                           id: string,
                                           query: string,
                                           documentData: DocumentData,
                                           clientId?: string,
                                           page?: string): Observable<any> => {

    return this.store.pipe(
        select(getCurrentLanguage),
        take(1),
        switchMap(currentLanguage => {
          return this.store.pipe(
              select(getFallbackLanguages),
              take(1),
              switchMap(fallbackLanguages => {
                const enrichmentData = this.enrichmentMaterialDetails();
                let client = clientId;

                // Fallback al client dal primo campo primario se non specificato
                if (!client && enrichmentData && enrichmentData.length > 0) {
                  const firstGroup = enrichmentData[0];
                  if (firstGroup.rows && firstGroup.rows.length > 0) {
                    const firstRow = firstGroup.rows[0];
                    const primaryKeys = Object.keys(firstRow.primary || {});
                    if (primaryKeys.length > 0) {
                      const primaryField = firstRow.primary[primaryKeys[0]];
                      client = primaryField?.client;
                    }
                  }
                }

                const requestBody: SmartCreationDropdownDataRequest = {
                  client: client || '',
                  language: currentLanguage,
                  fallbackLanguages,
                  queryText: query,
                  categoriesSelected: {},
                  materialFormControlList: [],
                  domain: '',
                  goldenRecordDetails: {},
                  completeness: null,
                  page: page || this.page,
                  processId: null,
                  materialId: null
                };

                return this.smartCreationDao.getDropdownData(source, id, requestBody).pipe(
                    catchError(error => {
                      console.error('Error fetching autocomplete suggestions:', error);
                      return of([]);
                    })
                );
              })
          );
        })
    );
  }

  /**
   * Wrapper function che corrisponde alla signature attesa per initFormGroup
   */
  private dynamicAutocompleteFn = (source: string, id: string, documentData: DocumentData): Observable<any> => {
    return this.dynamicAutocompleteFnInternal(source, id, '', documentData);
  }

  private initFormGroup(
      sheets: SmartFieldConfiguration[],
      page: string,
      viewMode: ViewModeEnum,
      sheetIndex: number,
      formGroup?: FormGroup,
      dynamicAutocompleteFn?: (source: string, id: string, documentData: DocumentData) => Observable<any>,
      initialData?: SmartCreationMaterialDetail,
      updateAlternativeUomFn?: (alternativeUomList: AlternativeUnitsOfMeasure[]) => void
  ) {
    const items = [];

    if (!formGroup) {
      formGroup = new FormGroup({});
    }

    sheets?.forEach((smartFieldConfiguration: SmartFieldConfiguration) => {
      formGroup.enable({ onlySelf: true, emitEvent: false });
      if (ObjectsUtils.isNoU(formGroup.controls?.[smartFieldConfiguration.id])) {
        const control: SmartCreationFormControl = this.initFormControl(smartFieldConfiguration);
        formGroup.addControl(smartFieldConfiguration.id, control);
      }

      normalizeControlsFormGroup(formGroup, sheets);
      smartFieldConfiguration.useTranslatePipe = setUseTranslatePipe(smartFieldConfiguration.id);

      const dynamicComponentWrapper: DynamicComponentWrapper = this.componentBasedOnType(
          smartFieldConfiguration,
          page,
          formGroup,
          sheetIndex,
          smartFieldConfiguration.order,
          dynamicAutocompleteFn,
          initialData,
          updateAlternativeUomFn,
          viewMode
      );

      items.push({
        ...dynamicComponentWrapper,
        ...smartFieldConfiguration
      });
    });

    return {
      formGroup,
      items
    };
  }

  getPrimaryInputConfig(fieldConfig: any): DynamicInputConfig | null {
    if (!fieldConfig || !fieldConfig.id) {
      return null;
    }
    return this.primaryInputConfigs().get(fieldConfig.id) || null;
  }

  getSecondaryInputConfig(fieldConfig: DeduplicationFieldConfiguration, materialKey?: string): DynamicInputConfig | null {
    if (!fieldConfig || !fieldConfig.id) {
      return null;
    }

    const key = materialKey ? `${fieldConfig.id}_${materialKey}` : fieldConfig.id;
    return this.secondaryInputConfigs().get(key) || null;
  }

  private componentBasedOnType(v: SmartFieldConfiguration,
                               page: string,
                               formGroup: FormGroup,
                               sheetIndex: number,
                               tabIndex: number,
                               dynamicAutocompleteFn?,
                               initialData?: SmartCreationMaterialDetail,
                               updateAlternativeUomFn?: (alternativeUomList: AlternativeUnitsOfMeasure[]) => void,
                               viewMode?: ViewModeEnum): DynamicComponentWrapper {
    return this.dynamicFormInputFactory.buildDynamicFormInput(v, page, formGroup, sheetIndex, tabIndex, viewMode, initialData, dynamicAutocompleteFn);
  }

  private initFormControl(ctrl: SmartFieldConfiguration): SmartCreationFormControl {
    const value = this.needArrayConversion(ctrl) ? ObjectsUtils.forceOntologyArray(ctrl?.value) : ctrl?.value;
    return new SmartCreationFormControl(value,
        ctrl.mandatory ? [Validators.required] : [],
        null,
        ctrl.label,
        ctrl.dropdownValues && ctrl.dropdownValues.length > 0 ? ctrl.dropdownValues : null,
        'smartCreation.smartValidation.error'
    );
  }

  private needArrayConversion(cfg: any) {
    return needFlatArray(cfg);
  }

  onInputValueEvent(id: string, formGroup?: FormGroup, sheetIndex?: number) {
    const inputConfig = this.primaryInputConfigs().get(id);
    if (!inputConfig?.formControl) {
      console.warn('No form control found for field:', id);
      return;
    }
    const formControl = inputConfig.formControl;
    const currValue = formControl.value;

    const processedValue = ObjectsUtils.isNotNoU(currValue) ? ObjectsUtils.flatArray(currValue) : null;
    this.service.updateEnrichmentPrimaryFieldValue({
      fieldId: id,
      value: processedValue
    });
    this.service.uncheckSecondaryFieldsForPrimaryField(id);
  }

  getSecondaryForColumn(row: EnrichmentTableRow, column: EnrichmentTableColumn): DeduplicationFieldConfiguration | null {
    if (column.type !== 'SECONDARY' || !column.materialKey) {
      return null;
    }
    return row.secondaries?.[column.materialKey] || null;
  }

  onSecondaryFieldSelectionChange(
      row: EnrichmentTableRow,
      column: EnrichmentTableColumn,
      secondaryField: DeduplicationFieldConfiguration,
      event: any
  ) {
    this.service.updateEnrichmentFieldSelection({
      materialKey: column.materialKey,
      client: this.signals?.currentClient(),
      fieldConfiguration: secondaryField
    });
  }

  onColumnSelectAll(column: EnrichmentTableColumn, selected: boolean) {
    if (column.materialKey) {
      this.service.bulkSelectEnrichmentColumn({
        columnMaterialId: column.materialKey,
        selected
      });
    }
  }

  isColumnAllSelected(column: EnrichmentTableColumn): boolean {
    if (!column.materialKey) {
      return false;
    }
    return this.columnSelectionStates()[column.materialKey]?.allSelected || false;
  }

  isColumnSomeSelected(column: EnrichmentTableColumn): boolean {
    if (!column.materialKey) {
      return false;
    }
    return this.columnSelectionStates()[column.materialKey]?.someSelected || false;
  }

  onToggleShowEnrichedData(showEnriched: boolean) {
    this.service.toggleShowEnrichedData(showEnriched);
  }

  onMoveAllValues() {
    const currentClient = this.signals?.currentClient();
    const enrichedData = this.signals?.enrichedMaterialsData();

    if (!currentClient || !enrichedData || !enrichedData[currentClient]) {
      console.warn('No enriched data available for current client:', currentClient);
      return;
    }

    const hasSelectedValues = this.hasSelectedSecondaryValues();

    if (!hasSelectedValues) {
      console.warn('No selected secondary values found to move');
      return;
    }

    this.service.moveAllSelectedValues();
  }

  private hasSelectedSecondaryValues(): boolean {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return false;
    }

    return data.some(group =>
      group.rows && group.rows.some((row: any) =>
        row.secondaries && Object.values(row.secondaries).some((secondary: any) =>
          secondary && secondary.selected && secondary.editable &&
          secondary.value && secondary.value !== ''
        )
      )
    );
  }

}
